package com.example.composetv.data

data class ContentItem(
    val id: String,
    val title: String,
    val description: String,
    val imageUrl: String,
    val backgroundImageUrl: String,
    val category: String,
    val rating: String,
    val duration: String,
    val year: String
)

object SampleData {
    val contentItems = listOf(
        ContentItem(
            id = "1",
            title = "Power Sisters",
            description = "A dynamic duo of superhero siblings join forces to save their city from a sinister villain, redefining sisterhood in action.",
            imageUrl = "https://picsum.photos/300/400?random=1",
            backgroundImageUrl = "https://picsum.photos/1920/1080?random=1",
            category = "Superhero/Action",
            rating = "U/A 13+",
            duration = "2h 15m",
            year = "2022"
        ),
        ContentItem(
            id = "2",
            title = "<PERSON> Strong",
            description = "An action-packed thriller featuring <PERSON> in his most challenging mission yet.",
            imageUrl = "https://picsum.photos/300/400?random=2",
            backgroundImageUrl = "https://picsum.photos/1920/1080?random=2",
            category = "Action/Thriller",
            rating = "U/A 16+",
            duration = "1h 58m",
            year = "2023"
        ),
        ContentItem(
            id = "3",
            title = "Yellow",
            description = "A mysterious drama that unfolds in unexpected ways, keeping viewers on the edge of their seats.",
            imageUrl = "https://picsum.photos/300/400?random=3",
            backgroundImageUrl = "https://picsum.photos/1920/1080?random=3",
            category = "Drama/Mystery",
            rating = "U/A 13+",
            duration = "2h 5m",
            year = "2022"
        ),
        ContentItem(
            id = "4",
            title = "2502",
            description = "A futuristic sci-fi thriller set in the year 2502, exploring humanity's future.",
            imageUrl = "https://picsum.photos/300/400?random=4",
            backgroundImageUrl = "https://picsum.photos/1920/1080?random=4",
            category = "Sci-Fi/Thriller",
            rating = "U/A 16+",
            duration = "2h 30m",
            year = "2023"
        ),
        ContentItem(
            id = "5",
            title = "The Last Stand",
            description = "An epic adventure that tests the limits of courage and determination.",
            imageUrl = "https://picsum.photos/300/400?random=5",
            backgroundImageUrl = "https://picsum.photos/1920/1080?random=5",
            category = "Adventure/Action",
            rating = "U/A 13+",
            duration = "2h 12m",
            year = "2023"
        )
    )
    
    val sciFiThrillers = contentItems.filter { it.category.contains("Sci-Fi") }
}

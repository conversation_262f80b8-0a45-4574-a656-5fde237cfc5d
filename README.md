# Compose TV Player

A TV application built with Compose for TV framework, featuring an immersive list layout based on Android TV design guidelines.

## Features

- **Immersive List Layout**: Implements the official Android TV immersive list design pattern
- **TV Navigation**: Proper D-pad/remote control navigation support
- **Focus Management**: TV-optimized focus handling and visual feedback
- **Default Styling**: Uses Compose for TV default components without custom theming
- **Content Browsing**: Featured content with dynamic background updates

## Architecture

- **Compose for TV**: Modern declarative UI framework for Android TV
- **Material 3**: Default Material Design 3 theming
- **Kotlin**: 100% Kotlin codebase
- **Coil**: Image loading and caching

## Project Structure

```
app/
├── src/main/java/com/example/composetv/
│   ├── MainActivity.kt                 # Main activity
│   ├── data/
│   │   └── ContentItem.kt             # Data models and sample data
│   └── ui/
│       ├── components/
│       │   ├── ContentCard.kt         # TV-optimized content card
│       │   └── ImmersiveListBackground.kt # Background component
│       ├── screen/
│       │   └── HomeScreen.kt          # Main home screen
│       └── theme/                     # Material 3 theme files
└── src/main/res/                      # Resources (strings, colors, etc.)
```

## Requirements

- Android Studio Arctic Fox or later
- Android SDK 21+ (Android 5.0)
- Java 8 or later
- Android TV emulator or physical Android TV device

## Setup and Installation

1. **Clone or download the project**
2. **Open in Android Studio**
3. **Sync Gradle dependencies**
4. **Run on Android TV emulator or device**

### Running on Android TV Emulator

1. Open Android Studio
2. Go to Tools > AVD Manager
3. Create a new Android TV virtual device
4. Select API level 21 or higher
5. Run the application

### Running on Physical Android TV Device

1. Enable Developer Options on your Android TV
2. Enable USB Debugging
3. Connect via USB or use wireless debugging
4. Run the application from Android Studio

## Usage

- Use the D-pad or remote control to navigate
- Focus on content cards to see background updates
- Press Enter/OK to select content items
- Navigate between rows using up/down arrows
- Navigate within rows using left/right arrows

## Design Reference

This application implements the official Android TV immersive list design pattern as specified in:
https://developer.android.com/design/ui/tv/guides/components/immersive-list

## Key Features Implemented

- ✅ Dynamic background updates based on focused content
- ✅ TV-optimized focus management
- ✅ Proper scaling and visual feedback for focused items
- ✅ Immersive full-screen layout
- ✅ Content rows with horizontal scrolling
- ✅ Default Compose for TV styling (no custom theming)

## Next Steps

This is the foundation for a TV application. Future enhancements could include:

- Video playback functionality
- Search capabilities
- User preferences and settings
- Content categories and filtering
- Network data integration
- User authentication

## License

This project is for demonstration purposes and follows Android TV design guidelines.

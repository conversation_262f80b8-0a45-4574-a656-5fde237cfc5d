package com.example.composetv.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.focusable
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.tv.foundation.lazy.list.TvLazyColumn
import androidx.tv.foundation.lazy.list.TvLazyRow
import androidx.tv.foundation.lazy.list.items
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.example.composetv.data.ContentItem
import com.example.composetv.data.SampleData
import com.example.composetv.ui.components.ContentCard
import com.example.composetv.ui.components.ImmersiveListBackground

@Composable
fun HomeScreen() {
    var selectedItem by remember { mutableStateOf(SampleData.contentItems.first()) }
    
    Box(modifier = Modifier.fillMaxSize()) {
        // Background with gradient overlay
        ImmersiveListBackground(
            backgroundImageUrl = selectedItem.backgroundImageUrl,
            modifier = Modifier.fillMaxSize()
        )
        
        // Content overlay
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(48.dp)
        ) {
            // Featured content info
            FeaturedContentInfo(
                contentItem = selectedItem,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            )
            
            // Content rows
            ContentRows(
                onItemSelected = { selectedItem = it },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun FeaturedContentInfo(
    contentItem: ContentItem,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.Center
    ) {
        // Rating and metadata
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = contentItem.rating,
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = contentItem.category,
                color = Color.White,
                fontSize = 14.sp
            )
            Text(
                text = contentItem.year,
                color = Color.White,
                fontSize = 14.sp
            )
            Text(
                text = contentItem.duration,
                color = Color.White,
                fontSize = 14.sp
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Title
        Text(
            text = contentItem.title,
            color = Color.White,
            fontSize = 48.sp,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Description
        Text(
            text = contentItem.description,
            color = Color.White,
            fontSize = 18.sp,
            lineHeight = 24.sp,
            modifier = Modifier.widthIn(max = 600.dp)
        )
    }
}

@Composable
private fun ContentRows(
    onItemSelected: (ContentItem) -> Unit,
    modifier: Modifier = Modifier
) {
    TvLazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(32.dp)
    ) {
        // Featured content row
        item {
            ContentRow(
                title = "Featured",
                items = SampleData.contentItems,
                onItemSelected = onItemSelected
            )
        }
        
        // Sci-fi thrillers row
        item {
            ContentRow(
                title = "Sci-fi thrillers",
                items = SampleData.sciFiThrillers + SampleData.contentItems.take(3),
                onItemSelected = onItemSelected
            )
        }
    }
}

@Composable
private fun ContentRow(
    title: String,
    items: List<ContentItem>,
    onItemSelected: (ContentItem) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = title,
            color = Color.White,
            fontSize = 24.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        TvLazyRow(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(items) { item ->
                ContentCard(
                    contentItem = item,
                    onItemSelected = onItemSelected,
                    modifier = Modifier
                        .size(width = 200.dp, height = 280.dp)
                        .focusable()
                )
            }
        }
    }
}
